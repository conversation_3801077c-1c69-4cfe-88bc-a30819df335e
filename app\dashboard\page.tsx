"use client";

import React, { useState } from "react";
import {
  Title,
  Text,
  Card,
  Grid,
  Box,
  Divider,
  Button,
  TextInput,
  Alert,
  Modal,
  Tooltip,
  Chip,
  useMantineTheme,
  rem,
} from "@mantine/core";
import { useDropzone } from "react-dropzone";
import { useMediaQuery } from "@mantine/hooks";
// Icon Imports
import { 
  IconTrash, 
  IconCopy, 
  IconSettings,
  IconClock,
  IconClockOff,
  IconUpload
} from "@tabler/icons-react";

// Hook Imports
import { useAuth } from "../hooks/useAuth";
import dayjs from "dayjs";

const FileUploadDropzone = ({
  onFileAccepted,
  uploadedByUserId,
  notesFieldName = "EnrollmentReportNotes",
}) => {
  const [errorMsg, setErrorMsg] = useState("");
  const [showError, setShowError] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [notes, setNotes] = useState("");
  const [showSuccessDialog, setShowSuccessDialog] = useState(false);
  const [fileUploading, setFileUploading] = useState(false);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop: (acceptedFiles) => {
      if (acceptedFiles && acceptedFiles.length > 0) {
        setSelectedFile(acceptedFiles[0]);
        onFileAccepted(acceptedFiles);
      }
    },
    onDropRejected: () => {
      setErrorMsg("Only Excel files up to 20MB are allowed.");
      setShowError(true);
    },
    accept: {
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet": [
        ".xlsx",
      ],
      "application/vnd.ms-excel": [".xls"],
    },
    maxSize: 20 * 1024 * 1024,
    multiple: false,
  });

  const handleRemoveFile = () => {
    setSelectedFile(null);
  };

  const handleUploadFile = async () => {
    setFileUploading(true);
    // Static UI only - no actual upload
    setTimeout(() => {
      setShowSuccessDialog(true);
      setSelectedFile(null);
      setNotes("");
      setFileUploading(false);
    }, 1500);
  };

  return (
    <>
      <Box
        {...getRootProps()}
        style={(theme) => ({
          border: `${rem(2)} dashed ${theme.colors.gray[4]}`,
          borderRadius: theme.radius.md,
          padding: theme.spacing.lg,
          textAlign: "center",
          cursor: "pointer",
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "center",
        })}
      >
        {/* Replace with your image or icon */}
        <IconUpload size={rem(60)} />
        <input {...getInputProps()} />
        {!selectedFile && (
          <>
            {isDragActive ? (
              <Text>Drop the files here ...</Text>
            ) : (
              <Text>Drag & drop file, or click to select...</Text>
            )}
          </>
        )}
        {selectedFile && (
          <Box
            mt="sm"
            style={{ 
              display: "flex", 
              alignItems: "center", 
              justifyContent: "space-between", 
              width: "100%", 
              borderTop: `${rem(1)} solid lightgray`, 
              paddingTop: theme.spacing.xs 
            }}
          >
            <Text
              size="sm"
              style={{ flexGrow: 1, textAlign: "left" }}
              lineClamp={1}
            >
              {selectedFile.name}
            </Text>
            <Button 
              variant="subtle" 
              color="red" 
              onClick={handleRemoveFile} 
              size="xs"
              p={rem(4)}
            >
              <IconTrash size={rem(16)} />
            </Button>
          </Box>
        )}
      </Box>
      {selectedFile && (
        <>
          <Box mt="sm">
            <TextInput
              label="Report Notes"
              value={notes}
              onChange={(e) => setNotes(e.currentTarget.value)}
              required
            />
          </Box>
          <Box mt="sm">
            <Button
              fullWidth
              onClick={handleUploadFile}
              disabled={!notes || fileUploading}
              loading={fileUploading}
            >
              {fileUploading ? "Uploading..." : "Upload File"}
            </Button>
          </Box>
        </>
      )}
      {showError && (
        <Alert 
          color="red" 
          title="Error" 
          mt="sm" 
          withCloseButton 
          onClose={() => setShowError(false)}
        >
          {errorMsg}
        </Alert>
      )}
      <Modal
        opened={showSuccessDialog}
        onClose={() => setShowSuccessDialog(false)}
        title="Success"
        centered
      >
        {/* Replace with your success animation or content */}
        <Text>File uploaded successfully!</Text>
        <Button 
          fullWidth 
          mt="md" 
          onClick={() => setShowSuccessDialog(false)}
        >
          OK
        </Button>
      </Modal>
    </>
  );
};

export default function DashboardPage() {
  const theme = useMantineTheme();
  const { user } = useAuth();
  const isSmallScreen = useMediaQuery(`(max-width: ${theme.breakpoints.md})`);

  // Static data for UI demonstration
  const enrolmentCount = 42;
  const taggingCount = 18;
  const studentResultsCount = 27;
  const tertiaryInstitutesCount = 15;
  const totalUsers = 83;

  // Static report settings data
  const reportSettings = [
    {
      reportType: "Enrolment Report",
      reportOpeningDate: dayjs().subtract(1, "week").toISOString(),
      reportClosingDate: dayjs().add(2, "weeks").toISOString(),
    },
    {
      reportType: "Student Results Report",
      reportOpeningDate: dayjs().subtract(3, "days").toISOString(),
      reportClosingDate: dayjs().add(1, "month").toISOString(),
    },
    {
      reportType: "Tagging Report",
      reportOpeningDate: dayjs().add(1, "day").toISOString(),
      reportClosingDate: dayjs().add(3, "weeks").toISOString(),
    },
  ];

  const enrolmentReportSetting = reportSettings.find(
    (setting) => setting.reportType === "Enrolment Report"
  );

  const studentResultsReportSetting = reportSettings.find(
    (setting) => setting.reportType === "Student Results Report"
  );

  const taggingReportSetting = reportSettings.find(
    (setting) => setting.reportType === "Tagging Report"
  );

  const now = dayjs();

  const enrolmentClosed = enrolmentReportSetting
    ? now.isBefore(dayjs(enrolmentReportSetting.reportOpeningDate)) ||
      now.isAfter(dayjs(enrolmentReportSetting.reportClosingDate))
    : false;

  const studentResultsClosed = studentResultsReportSetting
    ? now.isBefore(dayjs(studentResultsReportSetting.reportOpeningDate)) ||
      now.isAfter(dayjs(studentResultsReportSetting.reportClosingDate))
    : false;

  const taggingClosed = taggingReportSetting
    ? now.isBefore(dayjs(taggingReportSetting.reportOpeningDate)) ||
      now.isAfter(dayjs(taggingReportSetting.reportClosingDate))
    : false;

  const handleDownloadTemplate = (fileName) => {
    console.log(`Download template: ${fileName}`);
    // Static UI only - no actual download
  };

  return (
    <Grid p="md" my="md">
      {/* Welcome Card */}
      <Grid.Col span={12}>
        <Card
          shadow="sm"
          padding="lg"
          radius="md"
          style={{
            backgroundImage: `linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.9))`, // Add your background image URL here
            color: "white",
          }}
        >
          <Box
            style={{
              display: "flex",
              justifyContent: isSmallScreen ? "center" : "space-between",
              alignItems: "center",
              flexDirection: isSmallScreen ? "column" : "row",
            }}
          >
            {isSmallScreen ? (
              // Replace with your image
              <div style={{ width: "50%" }}>{/* <img src={PTEIWelcomeIcon} alt="Welcome Icon" width="100%" /> */}</div>
            ) : null}
            <Box
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: isSmallScreen ? "center" : "flex-start",
                marginTop: theme.spacing.xl,
              }}
            >
              <Title order={3} style={{ fontWeight: 600 }}>
                Welcome Back 👋
              </Title>
              <Title order={3} style={{ fontWeight: 800 }}>
                {user ? `${user.firstName} ${user.lastName}` : "Guest"}
              </Title>
              <Text
                style={{
                  fontWeight: 600,
                  marginTop: theme.spacing.xl,
                  opacity: "0.8",
                  textAlign: isSmallScreen ? "center" : "left",
                }}
              >
                The TSLS Partner Tertiary Education Institution Portal
              </Text>
            </Box>
            {!isSmallScreen && (
              // Replace with your image
              <div style={{ width: "20%" }}>{/* <img src={PTEIWelcomeIcon} alt="Welcome Icon" width="100%" /> */}</div>
            )}
          </Box>
        </Card>
      </Grid.Col>

      {/* Enrolment Reports Dropzone */}
      {user?.roleName === "University" && (
        <Grid.Col span={{ xs: 12, md: 4 }}>
          <Card shadow="sm" padding="lg" radius="md">
            <Box
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "column",
              }}
            >
              <Box
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexDirection: "row",
                  gap: theme.spacing.xs,
                }}
              >
                <Title order={4} style={{ fontWeight: 600, textTransform: "uppercase" }}>
                  Enrolment Reports
                </Title>
                <Tooltip label="🗃️ Download Enrolment Report Template" withArrow>
                  <Button 
                    variant="subtle" 
                    onClick={() => handleDownloadTemplate("Enrolment Report Template.xlstyle")}
                    p={rem(4)}
                  >
                    <IconCopy size={rem(20)} color={theme.colors.blue[6]} />
                  </Button>
                </Tooltip>
              </Box>
              <Divider 
                my="sm" 
                style={{ 
                  width: "30%", 
                  backgroundColor: theme.colors.blue[6], 
                  height: rem(2) 
                }} 
              />
              <Text style={{ fontWeight: 600, opacity: "0.8", textAlign: "center" }}>
                Accepting Submissions From:{" "}
              </Text>
              <Text size="sm" style={{ fontWeight: 500, opacity: "0.8", mb: "sm", textAlign: "center" }}>
                {enrolmentReportSetting ? (
                  <>
                    {dayjs(enrolmentReportSetting.reportOpeningDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}{" "}
                    to{" "}
                    {dayjs(enrolmentReportSetting.reportClosingDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}
                  </>
                ) : (
                  "TBA"
                )}
              </Text>
            </Box>

            {enrolmentClosed ? (
              <Box
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: theme.spacing.lg,
                }}
              >
                {/* Replace with your locked animation or icon */}
                <IconCircleOff size={rem(60)} />
                <Chip checked={false} color="blue" mt="sm">
                  Submissions Closed
                </Chip>
              </Box>
            ) : (
              <FileUploadDropzone
                onFileAccepted={(files) => console.log("Files:", files)}
                uploadedByUserId={user?.id}
              />
            )}
          </Card>
        </Grid.Col>
      )}

      {/* Tagging Reports Dropzone */}
      {user?.roleName === "University" && (
        <Grid.Col span={{ xs: 12, md: 4 }}>
          <Card shadow="sm" padding="lg" radius="md">
            <Box
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "column",
              }}
            >
              <Box
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexDirection: "row",
                  gap: theme.spacing.xs,
                }}
              >
                <Title order={4} style={{ fontWeight: 600, textTransform: "uppercase" }}>
                  Tagging Reports
                </Title>
                <Tooltip label="🗃️ Download Tagging Report Template" withArrow>
                  <Button 
                    variant="subtle" 
                    onClick={() => handleDownloadTemplate("Tagging Report Template.xlstyle")}
                    p={rem(4)}
                  >
                    <IconCopy size={rem(20)} color={theme.colors.blue[6]} />
                  </Button>
                </Tooltip>
              </Box>
              <Divider 
                my="sm" 
                style={{ 
                  width: "30%", 
                  backgroundColor: theme.colors.blue[6], 
                  height: rem(2) 
                }} 
              />
              <Text style={{ fontWeight: 600, opacity: "0.8", textAlign: "center" }}>
                Accepting Submissions From:{" "}
              </Text>
              <Text size="sm" style={{ fontWeight: 500, opacity: "0.8", mb: "sm", textAlign: "center" }}>
                {taggingReportSetting ? (
                  <>
                    {dayjs(taggingReportSetting.reportOpeningDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}{" "}
                    to{" "}
                    {dayjs(taggingReportSetting.reportClosingDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}
                  </>
                ) : (
                  "TBA"
                )}
              </Text>
            </Box>

            {taggingClosed ? (
              <Box
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: theme.spacing.lg,
                }}
              >
                {/* Replace with your locked animation or icon */}
                <IconClockOff size={rem(60)} />
                <Chip checked={false} color="blue" mt="sm">
                  Submissions Closed
                </Chip>
              </Box>
            ) : (
              <FileUploadDropzone
                onFileAccepted={(files) => console.log("Files:", files)}
                uploadedByUserId={user?.userId}
                notesFieldName="TaggingReportNotes"
              />
            )}
          </Card>
        </Grid.Col>
      )}

      {/* Student Results Reports Dropzone */}
      {user?.roleName === "TERTIARY INSTITUT" && (
        <Grid.Col span={{ xs: 12, md: 4 }}>
          <Card shadow="sm" padding="lg" radius="md">
            <Box
              style={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                flexDirection: "column",
              }}
            >
              <Box
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                  flexDirection: "row",
                  gap: theme.spacing.xs,
                }}
              >
                <Title order={4} style={{ fontWeight: 600, textTransform: "uppercase" }}>
                  Student Results
                </Title>
                <Tooltip label="🗃️ Download Student Results Report Template" withArrow>
                  <Button 
                    variant="subtle" 
                    onClick={() => handleDownloadTemplate("Student Results Report Template.xlstyle")}
                    p={rem(4)}
                  >
                    <IconCopy size={rem(20)} color={theme.colors.blue[6]} />
                  </Button>
                </Tooltip>
              </Box>
              <Divider 
                my="sm" 
                style={{ 
                  width: "30%", 
                  backgroundColor: theme.colors.blue[6], 
                  height: rem(2) 
                }} 
              />
              <Text style={{ fontWeight: 600, opacity: "0.8", textAlign: "center" }}>
                Accepting Submissions From:{" "}
              </Text>
              <Text size="sm" style={{ fontWeight: 500, opacity: "0.8", mb: "sm", textAlign: "center" }}>
                {studentResultsReportSetting ? (
                  <>
                    {dayjs(studentResultsReportSetting.reportOpeningDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}{" "}
                    to{" "}
                    {dayjs(studentResultsReportSetting.reportClosingDate)
                      .format("ddd D MMM, YYYY (hh:mm A)")
                      .replace(/^Tue/, "Tues")}
                  </>
                ) : (
                  "TBA"
                )}
              </Text>
            </Box>
            {studentResultsClosed ? (
              <Box
                style={{
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  justifyContent: "center",
                  padding: theme.spacing.lg,
                }}
              >
                {/* Replace with your locked animation or icon */}
                <IconClockOff size={rem(60)} />
                <Chip checked={false} color="blue" mt="sm">
                  Submissions Closed
                </Chip>
              </Box>
            ) : (
              <FileUploadDropzone
                onFileAccepted={(files) => console.log("Files:", files)}
                uploadedByUserId={user?.id}
                notesFieldName="ResultsReportNotes"
              />
            )}
          </Card>
        </Grid.Col>
      )}

      {/* Additional dashboard cards would follow the same pattern */}
      {/* For brevity, I've included the first three dropzone sections */}
      {/* The remaining cards would be converted similarly */}

      <Grid.Col span={12}>
        <Text mt="sm">
          This is the main dashboard area. You can customize this section with
          charts, stats, or quick links.
        </Text>

        <Card shadow="sm" padding="lg" mt="xl" radius="md" withBorder>
          <Text>This is a dashboard card example 🚀</Text>
        </Card>
      </Grid.Col>
    </Grid>
  );
}